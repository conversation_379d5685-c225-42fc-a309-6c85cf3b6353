import sbt.Keys._
import sbt._
import sbtrelease.ReleaseStateTransformations._
import sbtrelease.{Version, versionFormatError}
import sbtreleasenotes.ReleaseNotesTransformations._

ThisBuild / scalaVersion := "2.12.15"
ThisBuild / organization := "com.amadeus.airbi"

ThisBuild / libraryDependencySchemes += "org.scala-lang.modules" %% "scala-parser-combinators" % VersionScheme.Always // workaround for BDS-19684, the line can be removed once the US is resolved
ThisBuild / libraryDependencySchemes += "org.scala-lang.modules" %% "scala-xml"                % VersionScheme.Always // workaround for BDS-19684, the line can be removed once the US is resolved

// Resolve dependency conflicts for Java 17 upgrade
ThisBuild / evictionErrorLevel := Level.Warn

import sbt.Tests._
// Given tests, define test groups where each will have its own forked JVM for execution
// Currently there is one test group per *Spec.scala file
// Given that each test group has a different JVM, the SparkSession is not shared
def testGroups(tests: Seq[TestDefinition], baseDir: File): Seq[Group] = {
  val onlyDisplayConfluencePages = System.getProperty("onlyDisplayConfluencePages")
  tests
    .groupBy(t => t.name)
    .map { case (group, tests) =>
      val options = ForkOptions()
        .withWorkingDirectory(baseDir)
        .withRunJVMOptions(
          Vector(
            s"-Dtest.group=${group}",
            s"-Dtest.basedir=${baseDir}",
            s"-DonlyDisplayConfluencePages=${onlyDisplayConfluencePages}"
          ) ++ DefaultForkJavaOptions
        )
      new Group(group, tests, SubProcess(options))
    } toSeq
}

lazy val root = (project in file("."))
  .settings(
    name := "json2star-common-etl",
    publish / aggregate := false,
    publishLocal / aggregate := false,
    assembly / aggregate := false,
    version / aggregate := false,
    name / aggregate := false,
    organization / aggregate := false,
    coverageFailOnMinimum := true,
    coverageMinimumStmtTotal := 84,
    coverageMinimumBranchTotal := 73,
    coverageExcludedPackages := "",
    commonSettings,
    assemblySettings,
    testSettings,
    releaseSettings,
    releaseNotesSettings,
    publishSettings,
    publishAssemblySettings
  )
  .dependsOn(core, refdata, pnr, tktemd, corr, dcspax, dcsbag, skd, inv, ra, xrt)
  .aggregate(core, refdata, pnr, tktemd, corr, dcspax, dcsbag, skd, inv, ra, xrt)

// Main module json2star project
lazy val core = (project in file("core"))
  .settings(
    name := "json2star-common-etl-core",
    commonSettings,
    testSettings,
    publishSettings
  )

lazy val refdata = (project in file("refdata"))
  .settings(
    name := "json2star-common-etl-refdata",
    commonSettings,
    nonPublishSettings
  )

// Project module "pnr" to contain the mapping file and generated documentation
// It uses the sbt resourceGenerators to generate resource in the jar at build time
// It depends of the core project to have all dependencies
lazy val pnr = (project in file("pnr"))
  .settings(
    name := "json2star-common-etl-pnr",
    commonSettings,
    testSettings,
    nonPublishSettings
  )
  .dependsOn(core % "compile->compile;test->test")

// Project module "tktemd" to contain the mapping file and generated documentation
// It uses the sbt resourceGenerators to generate resource in the jar at build time
// It depends of the core project to have all dependencies
lazy val tktemd = (project in file("tktemd"))
  .settings(
    name := "json2star-common-etl-tktemd",
    commonSettings,
    testSettings,
    nonPublishSettings
  )
  .dependsOn(core % "compile->compile;test->test")

val DefaultForkJavaOptions = Seq(
  "-Dspark.driver.bindAddress=127.0.0.1",
  "-Duser.country.format=US",
  "-Duser.language.format=en",
  "-Duser.timezone=UTC",
  "-Xms2000M",
  "-Xmx4000M",
  "-XX:+UseCompressedOops",
  "-XX:+UseG1GC",
  // Java 17 specific options
  "--add-opens=java.base/java.lang=ALL-UNNAMED",
  "--add-opens=java.base/java.lang.invoke=ALL-UNNAMED",
  "--add-opens=java.base/java.lang.reflect=ALL-UNNAMED",
  "--add-opens=java.base/java.io=ALL-UNNAMED",
  "--add-opens=java.base/java.net=ALL-UNNAMED",
  "--add-opens=java.base/java.nio=ALL-UNNAMED",
  "--add-opens=java.base/java.util=ALL-UNNAMED",
  "--add-opens=java.base/java.util.concurrent=ALL-UNNAMED",
  "--add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED",
  "--add-opens=java.base/sun.nio.ch=ALL-UNNAMED",
  "--add-opens=java.base/sun.nio.cs=ALL-UNNAMED",
  "--add-opens=java.base/sun.security.action=ALL-UNNAMED",
  "--add-opens=java.base/sun.util.calendar=ALL-UNNAMED",
  "--add-opens=java.security.jgss/sun.security.krb5=ALL-UNNAMED"
)

lazy val corr = (project in file("corr"))
  .settings(
    name := "json2star-common-etl-corr",
    commonSettings,
    testSettings,
    nonPublishSettings
  )
  .dependsOn(core % "compile->compile;test->test")

lazy val dcspax = (project in file("dcspax"))
  .settings(
    name := "json2star-common-etl-dcspax",
    commonSettings,
    testSettings,
    nonPublishSettings
  )
  .dependsOn(core % "compile->compile;test->test")

lazy val dcsbag = (project in file("dcsbag"))
  .settings(
    name := "json2star-common-etl-dcsbag",
    commonSettings,
    testSettings,
    nonPublishSettings
  )
  .dependsOn(core % "compile->compile;test->test")

lazy val skd = (project in file("skd"))
  .settings(
    name := "json2star-common-etl-skd",
    commonSettings,
    testSettings,
    nonPublishSettings
  )
  .dependsOn(core % "compile->compile;test->test")

lazy val inv = (project in file("inv"))
  .settings(
    name := "json2star-common-etl-inv",
    commonSettings,
    testSettings,
    nonPublishSettings
  )
  .dependsOn(core % "compile->compile;test->test")

lazy val ra = (project in file("ra"))
  .settings(
    name := "json2star-common-etl-ra",
    commonSettings,
    testSettings,
    nonPublishSettings
  )
  .dependsOn(core % "compile->compile;test->test")

lazy val xrt = (project in file("xrt"))
  .settings(
    name := "json2star-common-etl-xrt",
    commonSettings,
    testSettings,
    nonPublishSettings
  )
  .dependsOn(core % "compile->compile;test->test")

val commonSettings = Seq(
  Compile / javacOptions ++= Seq("-source", "17", "-target", "17"),
  scalacOptions ++= Seq(
    "-Ypartial-unification",
    "-deprecation",
    "-feature",
    "-encoding",
    "UTF-8",
    "-target:jvm-17"
  ),
  libraryDependencies ++= Dependencies.coreDeps,
  isSnapshot := version.value.contains("SNAPSHOT"),
  scalastyleFailOnError := true
)

val forkedTestGroups = Option(System.getProperty("j2s.test.forkedTestGroups"))
  .map(_.toInt)
  .getOrElse(10) // scalastyle:ignore magic.number

val testSettings = Seq(
  libraryDependencies ++= Dependencies.testDeps,
  Test / testGrouping := testGroups((Test / definedTests).value, (Test / run / baseDirectory).value),
  Global / concurrentRestrictions := Seq(
    // Restriction: run N forked test groups concurrently
    Tags.limit(Tags.ForkedTestGroup, forkedTestGroups)
  ),
  Test / scalacOptions ++= Seq("-Yrangepos"),
  Test / parallelExecution := false, // Necessary for tests using Spark
  Test / fork := true, // Allow to have specific memory settings for running tests with Spark
  Test / javaOptions ++= Seq(), // don't set it up, will be ignored as test are run in forked JVMs, use .sbtopts instead
  Test / testOptions += Tests.Argument(TestFrameworks.ScalaTest, "-oD", "-u", "target/test-reports")
)

val assemblySettings = Seq(
  assembly / assemblyMergeStrategy ~= { mergeStrategy => entry =>
    {
      val strategy = mergeStrategy(entry)
      if (entry.endsWith(".conf")) { MergeStrategy.singleOrError }
      else {
        if (strategy == MergeStrategy.deduplicate) MergeStrategy.first else strategy
      }
    }
  },
  // Add shading configuration to avoid lib dependency issue
  assembly / assemblyShadeRules += {
    ShadeRule
      .rename(
        "com.fasterxml.jackson.core.**" -> "shaded.dbxsdk.com.fasterxml.jackson.@1"
      )
      .inLibrary(
        Library.databricksSdk
      )
  },
  assembly / assemblyJarName := name.value + "_" + scalaBinaryVersion.value + "-" + version.value + "-assembly.jar",
  assembly / test := {},
  assembly / assemblyOption := (assembly / assemblyOption).value.withIncludeScala(false)
)

val publishAssemblySettings = Seq(
  // Activate the assembly jar publication
  Compile / assembly / artifact ~= { _.withClassifier(Some("assembly")) }
) ++ addArtifact(Compile / assembly / artifact, assembly)

val publishSettings = Seq(
  publishTo := {
    val artifactory = "https://repository.rnd.amadeus.net/"
    if (isSnapshot.value) {
      Some("snapshots" at artifactory + "DDL-sbt-snapshot-ddl-nce/")
    } else {
      Some("releases" at artifactory + "mvn-production/")
    }
  },
  Test / publishArtifact := true
)

val nonPublishSettings = Seq(
  publish := {}
)

val releaseSettings = Seq(
  releaseCommitMessage := s"[sbt-release] Setting version to ${(ThisBuild / version).value}",
  releaseNextCommitMessage := s"[sbt-release] Setting version to ${(ThisBuild / version).value}",
  releaseVersion := { ver =>
    Version(ver)
      .map { v =>
        suggestedBump.value match {
          case Version.Bump.Bugfix => v.withoutQualifier.unapply // Already bumped by previous release
          case _ => v.bump(suggestedBump.value).withoutQualifier.unapply
        }
      }
      .getOrElse(versionFormatError(ver))
  },
  releaseProcess := Seq[ReleaseStep](
    checkSnapshotDependencies,
    inquireVersions,
    setReleaseVersion,
    commitReleaseVersion,
    updateReleaseNotes,
    tagRelease,
    pushChanges,
    publishArtifacts,
    setNextVersion,
    commitNextVersion,
    pushChanges
  ),
  bugfixRegexes := List(""".*\[bugfix\].*""", """.*\[fix\].*""", """.*\[technical\].*""").map(_.r),
  minorRegexes := List(""".*\[minor\].*""", """.*\[feature\].*""").map(_.r),
  majorRegexes := List(""".*\[major\].*""", """.*\[breaking\].*""").map(_.r)
)

val releaseNotesSettings = Seq(
  ThisBuild / releaseNotesNextVersionPlaceholder := "## NEXT_VERSION",
  ThisBuild / releaseNotesDraftTemplate := "### Changes\n$CHANGES\n\n### Upgrade actions\n...",
  ThisBuild / releaseNotesDraftExcludes := Seq("""README\.md""").map(_.r),
  ThisBuild / releaseNotesBranch := "master",
  releaseNotesUpdate / releaseNotesUpdateCommitMessage := s"[sbt-release] Update release notes for version ${(ThisBuild / version).value}"
)

val enableFortify = sys.props.get("enableFortify").map(_.toBoolean).getOrElse(false)

// Conditional settings for Fortify
ThisBuild / scalacOptions ++= {
  if (enableFortify)
    Seq(s"-P:fortify:build=json2star-common-etl")
  else
    Seq.empty
}
// The project now uses scalaVersion "2.12.15", which is compatible with Fortify requirements.
ThisBuild / libraryDependencies ++= {
  if (enableFortify)
    Seq(compilerPlugin("com.lightbend" % "scala-fortify_2.12.15" % "1.1.5").withCrossVersion(CrossVersion.disabled))
  else
    Seq.empty
}
