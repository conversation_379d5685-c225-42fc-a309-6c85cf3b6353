{"{\"organization\":\"net.snowflake\",\"name\":\"spark-snowflake\",\"revision\":\"2.15.0-spark_3.4\",\"configurations\":\"provided\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.apache.spark\",\"name\":\"spark-sql\",\"revision\":\"3.4.1\",\"configurations\":\"provided\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"io.delta\",\"name\":\"delta-core\",\"revision\":\"2.4.0\",\"configurations\":\"provided\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"net.sourceforge.plantuml\",\"name\":\"plantuml\",\"revision\":\"1.2024.7\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.scalamock\",\"name\":\"scalamock\",\"revision\":\"6.0.0\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.slf4j\",\"name\":\"slf4j-log4j12\",\"revision\":\"1.7.36\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"com.databricks\",\"name\":\"dbutils-api\",\"revision\":\"0.0.6\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.specs2\",\"name\":\"specs2-core\",\"revision\":\"4.20.5\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"com.github.mrpowers\",\"name\":\"spark-fast-tests\",\"revision\":\"1.3.0\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.apache.spark\",\"name\":\"spark-avro\",\"revision\":\"3.4.1\",\"configurations\":\"provided\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.rogach\",\"name\":\"scallop\",\"revision\":\"5.1.0\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"com.jayway.jsonpath\",\"name\":\"json-path\",\"revision\":\"2.9.0\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.typelevel\",\"name\":\"cats-kernel\",\"revision\":\"2.10.0\",\"configurations\":\"provided\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"com.github.pureconfig\",\"name\":\"pureconfig-yaml\",\"revision\":\"0.17.6\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.apache.spark\",\"name\":\"spark-sql\",\"revision\":\"3.4.1\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[{\"name\":\"spark-sql\",\"type\":\"jar\",\"extension\":\"jar\",\"classifier\":\"tests\",\"configurations\":[],\"extraAttributes\":{},\"allowInsecureProtocol\":false}],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.apache.spark\",\"name\":\"spark-core\",\"revision\":\"3.4.1\",\"configurations\":\"provided\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"com.amadeus.airbi\",\"name\":\"dih-datalake-common-spark\",\"revision\":\"4.2.8\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"com.github.pureconfig\",\"name\":\"pureconfig\",\"revision\":\"0.17.6\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.apache.spark\",\"name\":\"spark-core\",\"revision\":\"3.4.1\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[{\"name\":\"spark-core\",\"type\":\"jar\",\"extension\":\"jar\",\"classifier\":\"tests\",\"configurations\":[],\"extraAttributes\":{},\"allowInsecureProtocol\":false}],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"com.amadeus.ti\",\"name\":\"ti-reports-coupons-api\",\"revision\":\"3.5.6\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"com.typesafe.scala-logging\",\"name\":\"scala-logging\",\"revision\":\"3.9.5\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"com.azure\",\"name\":\"azure-identity\",\"revision\":\"1.13.2\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"com.databricks\",\"name\":\"databricks-sdk-java\",\"revision\":\"0.16.0\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[{\"organization\":\"com.fasterxml.jackson.core\",\"name\":\"jackson-databind\",\"artifact\":\"*\",\"configurations\":[],\"crossVersion\":{\"type\":\"Disabled\"}}],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.scalatest\",\"name\":\"scalatest\",\"revision\":\"3.2.18\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.specs2\",\"name\":\"specs2-junit\",\"revision\":\"4.20.5\",\"configurations\":\"test\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"org.scala-lang\",\"name\":\"scala-library\",\"revision\":\"2.12.15\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}, "{\"organization\":\"com.amadeus.airbi\",\"name\":\"dih-datalake-common\",\"revision\":\"4.2.8\",\"configurations\":\"compile\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Binary\",\"prefix\":\"\",\"suffix\":\"\"}}": {"value": {"$fields": ["path", "startLine"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/build.sbt", "startLine": 208}, "type": "LinePosition"}}