{"files.eol": "\n", "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "files.exclude": {"**/target": true, "**/.bloop": true, "**/.metals": true}, "search.exclude": {"**/target": true, "**/.bloop": true, "**/.metals": true}, "metals.javaHome": "${env:JAVA_HOME}", "metals.sbtScript": "sbt", "metals.bloopSbtAlreadyInstalled": true, "metals.serverVersion": "latest.snapshot", "java.configuration.runtimes": [{"name": "JavaSE-17", "path": "${env:JAVA_HOME}"}], "java.compile.nullAnalysis.mode": "automatic", "java.configuration.checkProjectSettingsExclusions": false, "scala.suggest.bracketAutoClosing": true, "editor.suggestSelection": "first", "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "[scala]": {"editor.defaultFormatter": "scalameta.metals", "editor.formatOnSave": true, "editor.tabSize": 2}, "[sbt]": {"editor.defaultFormatter": "scalameta.metals", "editor.formatOnSave": true, "editor.tabSize": 2}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features", "editor.formatOnSave": true}, "[markdown]": {"editor.formatOnSave": false, "editor.wordWrap": "on"}, "terminal.integrated.env.windows": {"JAVA_OPTS": "-Xms2048M -Xmx2048M -Xss16M"}, "terminal.integrated.env.linux": {"JAVA_OPTS": "-Xms2048M -Xmx2048M -Xss16M"}, "terminal.integrated.env.osx": {"JAVA_OPTS": "-Xms2048M -Xmx2048M -Xss16M"}, "files.watcherExclude": {"**/target": true}}