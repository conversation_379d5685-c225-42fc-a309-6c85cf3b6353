import sbt.Keys.streams
import sbt._

object DepsVersion {
  val spark         = "3.4.1"
  val deltaVersion  = "2.4.0" // https://docs.delta.io/latest/releases.html
  val dbutils       = "0.0.6"
  val databricksSdk = "0.16.0"

  val cats                = "2.10.0"
  val scalaTest           = "3.2.18"
  val specs2              = "4.20.5"
  val pureconfig          = "0.17.6"
  val jsonSchemaValidator = "2.2.6"
  val jsonpath =
    "2.9.0" // update for SUPPRESS_EXCEPTIONS performance gain: https://github.com/json-path/JsonPath/pull/767
  val scalaLogging = "3.9.5"
  val slf4jLog4j   = "1.7.36"
  val playVersion  = "2.10.4"

  val dihCommonVersion = "4.2.8"
  val sparkFastTests   = "1.3.0"

  val snowflakeSpark      = "2.15.0-spark_3.4"
  val scallop             = "5.1.0"
  val pureConfigYaml      = "0.17.6"
  // Refer to https://rndwww.nce.amadeus.net/git/projects/ATI/repos/ti-reports-coupons/browse/doc/release_notes.md
  val tiReportsCouponsApiVersion = "3.5.6"
  val plantUml            = "1.2024.7"
}

object Library {

  def compileIfLocalOtherwiseProvided(): Configuration = {
    if (Option(System.getProperty("local")).mkString.equalsIgnoreCase("true")) {
      Compile
    } else {
      Provided
    }
  }

  // To exclude incompatible transitive dependency
  // - when running tests locally, it is excluded as not used
  // - when running app on cluster, it is shaded during the assembly step
  // This workaround avoids having an additional step (assembly) before running tests
  // as it is more complex to maintain in the dev-env (sbt, intellij, ...)
  def excludeDepIfLocal(m: ModuleID, org: String, name: String): ModuleID = {
    if (Option(System.getProperty("j2s.test.local")).mkString.equalsIgnoreCase("true")) {
      // println(s"Exclude dependency ${org} ${name} for ${m.name}")
      m excludeAll ExclusionRule(org, name)
    } else {
      m
    }
  }

  val sparkCore           = "org.apache.spark"           %% "spark-core"                % DepsVersion.spark
  val sparkSql            = "org.apache.spark"           %% "spark-sql"                 % DepsVersion.spark
  val avro                = "org.apache.spark"           %% "spark-avro"                % DepsVersion.spark
  val specs2              = "org.specs2"                 %% "specs2-core"               % DepsVersion.specs2
  val specs2Junit         = "org.specs2"                 %% "specs2-junit"              % DepsVersion.specs2
  val catsKernel          = "org.typelevel"              %% "cats-kernel"               % DepsVersion.cats
  val pureconfig          = "com.github.pureconfig"      %% "pureconfig"                % DepsVersion.pureconfig
  val jsonpath            = "com.jayway.jsonpath"         % "json-path"                 % DepsVersion.jsonpath
  val scalaLogging        = "com.typesafe.scala-logging" %% "scala-logging"             % DepsVersion.scalaLogging
  val slf4jLog4j          = "org.slf4j"                   % "slf4j-log4j12"             % DepsVersion.slf4jLog4j
  val dbutils             = "com.databricks"             %% "dbutils-api"               % DepsVersion.dbutils
  val databricksSdk       = "com.databricks"              % "databricks-sdk-java"       % DepsVersion.databricksSdk
  val playJson            = "com.typesafe.play"          %% "play-json"                 % DepsVersion.playVersion
  val delta               = "io.delta"                   %% "delta-core"                % DepsVersion.deltaVersion
  val scalaTest           = "org.scalatest"              %% "scalatest"                 % DepsVersion.scalaTest
  val dihCommon           = "com.amadeus.airbi"          %% "dih-datalake-common"       % DepsVersion.dihCommonVersion
  val dihCommonSpark      = "com.amadeus.airbi"          %% "dih-datalake-common-spark" % DepsVersion.dihCommonVersion
  val sparkFastTests      = "com.github.mrpowers"        %% "spark-fast-tests"          % DepsVersion.sparkFastTests
  val snowflakeSpark      = "net.snowflake"              %% "spark-snowflake"           % DepsVersion.snowflakeSpark
  val scallop             = "org.rogach"                 %% "scallop"                   % DepsVersion.scallop
  val pureConfigYaml      = "com.github.pureconfig"      %% "pureconfig-yaml"           % DepsVersion.pureConfigYaml
  val azureIdentity       = "com.azure"                   % "azure-identity"            % "1.13.2"
  val scalaMock           = "org.scalamock"              %% "scalamock"                 % "6.0.0"
  val tiReportsCouponsApi = "com.amadeus.ti"             %% "ti-reports-coupons-api"    % DepsVersion.tiReportsCouponsApiVersion
  val plantUml            = "net.sourceforge.plantuml"    % "plantuml"                  % DepsVersion.plantUml
}

object Dependencies {

  import Library._

  val coreDeps = Seq(
    sparkCore           % compileIfLocalOtherwiseProvided,
    sparkSql            % compileIfLocalOtherwiseProvided,
    avro                % Provided,
    catsKernel          % Provided,
    delta               % Provided,
    pureconfig          % Compile,
    jsonpath            % Compile,
    scalaLogging        % Compile,
    dihCommon           % Compile,
    dihCommonSpark      % Compile,
    dbutils             % Compile,
    snowflakeSpark      % Provided,
    scallop             % Compile,
    pureConfigYaml      % Compile,
    azureIdentity       % Compile,
    excludeDepIfLocal(databricksSdk % Compile, "com.fasterxml.jackson.core", "jackson-databind"),
    tiReportsCouponsApi % Compile,
    plantUml            % Compile
  )

  val testDeps = Seq(
    specs2         % Test,
    specs2Junit    % Test,
    slf4jLog4j     % Test,
    sparkCore      % Test classifier "tests",
    sparkSql       % Test classifier "tests",
    scalaTest      % Test,
    sparkFastTests % Test,
    scalaMock      % Test
  )
}
