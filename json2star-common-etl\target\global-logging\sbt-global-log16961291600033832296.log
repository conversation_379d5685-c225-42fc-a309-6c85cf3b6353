[0m[[0m[0mdebug[0m] [0m[0m> Exec(early(addDefaultCommands), None, None)[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(addDefaultCommands, None, None)[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(early(initialize), None, None)[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(initialize, None, None)[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(boot, None, None)[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(writeSbtVersion, None, None)[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(reload, None, None)[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(sbtStashOnFailure, None, None)[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(onFailure loadFailed, None, None)[0m
[0m[[0m[0mdebug[0m] [0m[0m> Exec(loadp, None, None)[0m
[0m[[0m[0minfo[0m] [0m[0mwelcome to sbt 1.9.8 (Ubuntu Java 17.0.15)[0m
[0m[[0m[0mdebug[0m] [0m[0mLoad.defaultLoad until apply took 56.001114ms[0m
[0m[[0m[0mdebug[0m] [0m[0m          Load.loadUnit: plugins took 35.59068ms[0m
[0m[[0m[0mdebug[0m] [0m[0m          Load.loadUnit: defsScala took 0.151405ms[0m
[0m[[0m[0mdebug[0m] [0m[0m[Loading] Scanning directory /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project[0m
[0m[[0m[0mdebug[0m] [0m[0m            Load.loadUnit: mkEval took 5.482497ms[0m
[0m[[0m[0mdebug[0m] [0m[0m[Loading] Found non-root projects [0m
[0m[[0m[0mdebug[0m] [0m[0m[Loading] Done in /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project, returning: ()[0m
[0m[[0m[0mdebug[0m] [0m[0mdeducing auto plugins based on known facts Set(Atom(sbt.plugins.CorePlugin)) and clauses Clauses(Clause(Atom(sbt.plugins.JvmPlugin),Set(Atom(sbt.plugins.IvyPlugin)))[0m
[0m[[0m[0mdebug[0m] [0m[0mClause(Atom(sbt.ScriptedPlugin),Set(Atom(sbt.plugins.JvmPlugin)))[0m
[0m[[0m[0mdebug[0m] [0m[0mClause(Atom(sbt.plugins.SbtPlugin),Set(Atom(sbt.ScriptedPlugin)))[0m
[0m[[0m[0mdebug[0m] [0m[0mClause(Atom(sbt.plugins.SemanticdbPlugin),Set(Atom(sbt.plugins.JvmPlugin)))[0m
[0m[[0m[0mdebug[0m] [0m[0mClause(Atom(sbt.plugins.JUnitXmlReportPlugin),Set(Atom(sbt.plugins.JvmPlugin)))[0m
[0m[[0m[0mdebug[0m] [0m[0mClause(Atom(sbt.plugins.MiniDependencyTreePlugin),Set(Atom(sbt.plugins.JvmPlugin)))[0m
[0m[[0m[0mdebug[0m] [0m[0mClause(Atom(sbt.plugins.CorePlugin),Set(Atom(sbt.plugins.IvyPlugin)))[0m
[0m[[0m[0mdebug[0m] [0m[0mClause(Atom(sbt.plugins.IvyPlugin),Set(Atom(sbt.plugins.JvmPlugin)))[0m
[0m[[0m[0mdebug[0m] [0m[0mClause(Atom(sbt.plugins.JvmPlugin),Set(Atom(sbt.plugins.SemanticdbPlugin)))[0m
[0m[[0m[0mdebug[0m] [0m[0mClause(Atom(sbt.plugins.JvmPlugin),Set(Atom(sbt.plugins.JUnitXmlReportPlugin)))[0m
[0m[[0m[0mdebug[0m] [0m[0mClause(Atom(sbt.plugins.CorePlugin),Set(Atom(sbt.plugins.Giter8TemplatePlugin)))[0m
[0m[[0m[0mdebug[0m] [0m[0mClause(Atom(sbt.plugins.JvmPlugin),Set(Atom(sbt.plugins.MiniDependencyTreePlugin))))[0m
[0m[[0m[0mdebug[0m] [0m[0m  :: deduced result: Matched(sbt.plugins.CorePlugin,sbt.plugins.Giter8TemplatePlugin,sbt.plugins.IvyPlugin,sbt.plugins.JvmPlugin,sbt.plugins.MiniDependencyTreePlugin,sbt.plugins.JUnitXmlReportPlugin,sbt.plugins.SemanticdbPlugin)[0m
[0m[[0m[0mdebug[0m] [0m[0mPlugins.deducer#function took 12.199639 ms[0m
[0m[[0m[0minfo[0m] [0m[0mloading settings for project json2star-common-etl-build from credentials.sbt,plugins.sbt ...[0m
[0m[[0m[0mdebug[0m] [0m[0m              Load.resolveProject(json2star-common-etl-build) took 26.807865ms[0m
[0m[[0m[0mdebug[0m] [0m[0m            Load.loadTransitive: finalizeProject(Project(id json2star-common-etl-build, base: /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project, plugins: List(<none>))) took 48.464744ms[0m
[0m[[0m[0mdebug[0m] [0m[0m[Loading] Done in /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project, returning: (json2star-common-etl-build)[0m
[0m[[0m[0mdebug[0m] [0m[0m          Load.loadUnit: loadedProjectsRaw took 555.962101ms[0m
[0m[[0m[0mdebug[0m] [0m[0m          Load.loadUnit: cleanEvalClasses took 365.630954ms[0m
[0m[[0m[0mdebug[0m] [0m[0m        Load.loadUnit(file:/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/, ...) took 961.781ms[0m
[0m[[0m[0mdebug[0m] [0m[0m      Load.apply: load took 1045.659018ms[0m
[0m[[0m[0mdebug[0m] [0m[0m      Load.apply: resolveProjects took 1.820166ms[0m
[0m[[0m[0mdebug[0m] [0m[0m      Load.apply: finalTransforms took 31.540534ms[0m
[0m[[0m[0mdebug[0m] [0m[0m      Load.apply: config.delegates took 2.722998ms[0m
[0m[[0m[0mdebug[0m] [0m[0m      Load.apply: Def.make(settings)... took 205.910808ms[0m
[0m[[0m[0mdebug[0m] [0m[0m      Load.apply: structureIndex took 57.778579ms[0m
[0m[[0m[0mdebug[0m] [0m[0m      Load.apply: mkStreams took 0.822129ms[0m
[0m[[0m[0minfo[0m] [0m[0mloading project definition from /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project[0m
[0m[[0m[0mdebug[0m] [0m[0mRunning task... Cancel: Signal, check cycles: false, forcegc: true[0m
[0m[[0m[33mwarn[0m] [0m[0mCredentials file /home/<USER>/.artifactory/credentials does not exist[0m
[0m[[0m[33mwarn[0m] [0m[0mCredentials file /home/<USER>/.artifactory/credentials does not exist[0m
[0m[[0m[33mwarn[0m] [0m[0mCredentials file /home/<USER>/.artifactory/credentials does not exist[0m
[0m[[0m[0mdebug[0m] [0m[0m    Load.loadUnit: plugins took 2490.247188ms[0m
[0m[[0m[0mdebug[0m] [0m[0m    Load.loadUnit: defsScala took 0.0026ms[0m
[0m[[0m[0mdebug[0m] [0m[0m[Loading] Scanning directory /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl[0m
[0m[[0m[0mdebug[0m] [0m[0m      Load.loadUnit: mkEval took 2.055874ms[0m
