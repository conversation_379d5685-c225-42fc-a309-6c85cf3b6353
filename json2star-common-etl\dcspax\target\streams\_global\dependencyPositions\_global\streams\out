[0m[[0m[31merror[0m] [0m[0mjava.nio.file.FileSystemException: /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/dcspax/target/streams/_global/dependencyPositions/_global/streams/update_cache_2.12/output_dsp: Operation not permitted[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/sun.nio.fs.UnixException.translateToIOException(UnixException.java:100)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/sun.nio.fs.UnixException.rethrowAsIOException(UnixException.java:106)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/sun.nio.fs.UnixException.rethrowAsIOException(UnixException.java:111)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/sun.nio.fs.UnixFileAttributeViews$Basic.setTimes(UnixFileAttributeViews.java:142)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.nio.file.Files.setLastModifiedTime(Files.java:2442)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.io.JavaMilli$.$anonfun$setModifiedTime$1(JavaMilli.scala:26)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.io.JavaMilli$.mapNoSuchFileException(JavaMilli.scala:32)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.io.JavaMilli$.setModifiedTime(JavaMilli.scala:25)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.io.Milli$.setModifiedTime(Milli.scala:389)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.io.IO$.$anonfun$setModifiedTimeOrFalse$1(IO.scala:1461)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.io.Retry$.apply$mVc$sp(Retry.scala:47)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.io.Retry$.apply$mVc$sp(Retry.scala:29)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.io.Retry$.apply$mVc$sp(Retry.scala:24)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.io.IO$.setModifiedTimeOrFalse(IO.scala:1461)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.InMemoryCacheStore$CacheStoreImpl.write(InMemoryCacheStore.scala:78)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.util.Tracked$.$anonfun$lastOutput$1(Tracked.scala:75)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.Classpaths$.$anonfun$dependencyPositionsTask$5(Defaults.scala:3865)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.Classpaths$.$anonfun$dependencyPositionsTask$5$adapted(Defaults.scala:3831)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.util.Tracked$.$anonfun$inputChangedW$1(Tracked.scala:220)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.Classpaths$.$anonfun$dependencyPositionsTask$1(Defaults.scala:3867)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.Function1.$anonfun$compose$1(Function1.scala:49)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.util.$tilde$greater.$anonfun$$u2219$1(TypeFunctions.scala:63)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.std.Transform$$anon$4.work(Transform.scala:69)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.Execute.$anonfun$submit$2(Execute.scala:283)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.util.ErrorHandling$.wideConvert(ErrorHandling.scala:24)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.Execute.work(Execute.scala:292)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.Execute.$anonfun$submit$1(Execute.scala:283)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.ConcurrentRestrictions$$anon$4.$anonfun$submitValid$1(ConcurrentRestrictions.scala:265)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.CompletionService$$anon$2.call(CompletionService.scala:65)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.lang.Thread.run(Thread.java:840)[0m
[0m[[0m[31merror[0m] [0m[0m(dcspax / [31mdependencyPositions[0m) java.nio.file.FileSystemException: /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/dcspax/target/streams/_global/dependencyPositions/_global/streams/update_cache_2.12/output_dsp: Operation not permitted[0m
