[0m[[0m[0mdebug[0m] [0m[0mCreated transactional ClassFileManager with tempDir = /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/target/scala-2.12/sbt-1.0/classes.bak[0m
[0m[[0m[0mdebug[0m] [0m[0mRemoving the temporary directory used for backing up class files: /mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/target/scala-2.12/sbt-1.0/classes.bak[0m
