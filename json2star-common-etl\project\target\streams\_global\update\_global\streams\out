[0m[[0m[0mdebug[0m] [0m[0mnot up to date. inChanged = true, force = false[0m
[0m[[0m[0mdebug[0m] [0m[0mUpdating ProjectRef(uri("file:/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/"), "json2star-common-etl-build")...[0m
[0m[[0m[0mdebug[0m] [0m[0mDone updating ProjectRef(uri("file:/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/"), "json2star-common-etl-build")[0m
[0m[[0m[31merror[0m] [0m[0mjava.lang.RuntimeException: found version conflict(s) in library dependencies; some are suspected to be binary incompatible:[0m
[0m[[0m[31merror[0m] [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m	* org.scala-lang.modules:scala-xml_2.12:2.3.0 (early-semver) is selected over 1.0.6[0m
[0m[[0m[31merror[0m] [0m[0m	    +- org.scoverage:scalac-scoverage-reporter_2.12:2.1.1 (depends on 2.3.0)[0m
[0m[[0m[31merror[0m] [0m[0m	    +- org.scalariform:scalariform_2.12:0.2.0             (depends on 1.0.6)[0m
[0m[[0m[31merror[0m] [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0mthis can be overridden using libraryDependencySchemes or evictionErrorLevel[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.sys.package$.error(package.scala:30)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.LibraryManagement$.resolve$1(LibraryManagement.scala:90)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.LibraryManagement$.$anonfun$cachedUpdate$12(LibraryManagement.scala:134)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.util.Tracked$.$anonfun$lastOutput$1(Tracked.scala:74)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.LibraryManagement$.$anonfun$cachedUpdate$20(LibraryManagement.scala:147)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.util.control.Exception$Catch.apply(Exception.scala:228)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.LibraryManagement$.$anonfun$cachedUpdate$11(LibraryManagement.scala:147)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.LibraryManagement$.$anonfun$cachedUpdate$11$adapted(LibraryManagement.scala:128)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.util.Tracked$.$anonfun$inputChangedW$1(Tracked.scala:220)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.LibraryManagement$.cachedUpdate(LibraryManagement.scala:161)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.Classpaths$.$anonfun$updateTask0$1(Defaults.scala:3801)[0m
[0m[[0m[31merror[0m] [0m[0m	at scala.Function1.$anonfun$compose$1(Function1.scala:49)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.util.$tilde$greater.$anonfun$$u2219$1(TypeFunctions.scala:63)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.std.Transform$$anon$4.work(Transform.scala:69)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.Execute.$anonfun$submit$2(Execute.scala:283)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.internal.util.ErrorHandling$.wideConvert(ErrorHandling.scala:24)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.Execute.work(Execute.scala:292)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.Execute.$anonfun$submit$1(Execute.scala:283)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.ConcurrentRestrictions$$anon$4.$anonfun$submitValid$1(ConcurrentRestrictions.scala:265)[0m
[0m[[0m[31merror[0m] [0m[0m	at sbt.CompletionService$$anon$2.call(CompletionService.scala:65)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)[0m
[0m[[0m[31merror[0m] [0m[0m	at java.base/java.lang.Thread.run(Thread.java:840)[0m
[0m[[0m[31merror[0m] [0m[0m([31mupdate[0m) found version conflict(s) in library dependencies; some are suspected to be binary incompatible:[0m
[0m[[0m[31merror[0m] [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m	* org.scala-lang.modules:scala-xml_2.12:2.3.0 (early-semver) is selected over 1.0.6[0m
[0m[[0m[31merror[0m] [0m[0m	    +- org.scoverage:scalac-scoverage-reporter_2.12:2.1.1 (depends on 2.3.0)[0m
[0m[[0m[31merror[0m] [0m[0m	    +- org.scalariform:scalariform_2.12:0.2.0             (depends on 1.0.6)[0m
[0m[[0m[31merror[0m] [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0m[0m
[0m[[0m[31merror[0m] [0m[0mthis can be overridden using libraryDependencySchemes or evictionErrorLevel[0m
