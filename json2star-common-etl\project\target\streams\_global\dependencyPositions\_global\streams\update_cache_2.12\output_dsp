{"{\"organization\":\"io.github.generoso\",\"name\":\"sbt-release-notes\",\"revision\":\"0.0.1\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/plugins.sbt", "range": {"$fields": ["start", "end"], "start": 8, "end": 9}}, "type": "RangePosition"}, "{\"organization\":\"org.scalameta\",\"name\":\"sbt-scalafmt\",\"revision\":\"2.5.2\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/plugins.sbt", "range": {"$fields": ["start", "end"], "start": 8, "end": 9}}, "type": "RangePosition"}, "{\"organization\":\"com.eed3si9n\",\"name\":\"sbt-assembly\",\"revision\":\"2.1.5\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/plugins.sbt", "range": {"$fields": ["start", "end"], "start": 8, "end": 9}}, "type": "RangePosition"}, "{\"organization\":\"me.pdalpra\",\"name\":\"sbt-autoversion\",\"revision\":\"2.0.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/plugins.sbt", "range": {"$fields": ["start", "end"], "start": 8, "end": 9}}, "type": "RangePosition"}, "{\"organization\":\"org.scala-lang\",\"name\":\"scala-library\",\"revision\":\"2.12.18\",\"configurations\":\"provided\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/plugins.sbt", "range": {"$fields": ["start", "end"], "start": 8, "end": 9}}, "type": "RangePosition"}, "{\"organization\":\"com.github.sbt\",\"name\":\"sbt-release\",\"revision\":\"1.4.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/plugins.sbt", "range": {"$fields": ["start", "end"], "start": 8, "end": 9}}, "type": "RangePosition"}, "{\"organization\":\"org.scalastyle\",\"name\":\"scalastyle-sbt-plugin\",\"revision\":\"1.0.0\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[{\"organization\":\"org.scala-lang.modules\",\"name\":\"scala-xml_2.12\",\"artifact\":\"*\",\"configurations\":[],\"crossVersion\":{\"type\":\"Disabled\"}}],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/plugins.sbt", "range": {"$fields": ["start", "end"], "start": 8, "end": 9}}, "type": "RangePosition"}, "{\"organization\":\"org.scoverage\",\"name\":\"sbt-scoverage\",\"revision\":\"2.0.12\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/plugins.sbt", "range": {"$fields": ["start", "end"], "start": 8, "end": 9}}, "type": "RangePosition"}, "{\"organization\":\"org.scala-sbt\",\"name\":\"sbt-dependency-tree\",\"revision\":\"1.9.8\",\"isChanging\":false,\"isTransitive\":true,\"isForce\":false,\"explicitArtifacts\":[],\"inclusions\":[],\"exclusions\":[],\"extraAttributes\":{\"e:sbtVersion\":\"1.0\",\"e:scalaVersion\":\"2.12\"},\"crossVersion\":{\"type\":\"Disabled\"}}": {"value": {"$fields": ["path", "range"], "path": "/mnt/c/Users/<USER>/coderepos/BDP-scala-upgrade/json2star-common-etl/project/plugins.sbt", "range": {"$fields": ["start", "end"], "start": 8, "end": 9}}, "type": "RangePosition"}}