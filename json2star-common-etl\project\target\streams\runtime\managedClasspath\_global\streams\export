/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scoverage/sbt-scoverage_2.12_1.0/2.0.12/sbt-scoverage_2.12_1.0-2.0.12.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/eed3si9n/sbt-assembly_2.12_1.0/2.1.5/sbt-assembly_2.12_1.0-2.1.5.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scalameta/sbt-scalafmt_2.12_1.0/2.5.2/sbt-scalafmt_2.12_1.0-2.5.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/sbt/sbt-release_2.12_1.0/1.4.0/sbt-release_2.12_1.0-1.4.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/me/pdalpra/sbt-autoversion_2.12_1.0/2.0.0/sbt-autoversion-2.0.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scalastyle/scalastyle-sbt-plugin_2.12_1.0/1.0.0/scalastyle-sbt-plugin-1.0.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/github/generoso/sbt-release-notes_2.12_1.0/0.0.1/sbt-release-notes_2.12_1.0-0.0.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scala-sbt/sbt-dependency-tree_2.12_1.0/1.9.8/sbt-dependency-tree_2.12_1.0-1.9.8.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scoverage/scalac-scoverage-plugin_2.12.19/2.1.1/scalac-scoverage-plugin_2.12.19-2.1.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scoverage/scalac-scoverage-reporter_2.12/2.1.1/scalac-scoverage-reporter_2.12-2.1.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scoverage/scalac-scoverage-domain_2.12/2.1.1/scalac-scoverage-domain_2.12-2.1.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scoverage/scalac-scoverage-serializer_2.12/2.1.1/scalac-scoverage-serializer_2.12-2.1.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/eed3si9n/jarjarabrams/jarjar-abrams-core_2.12/1.13.1/jarjar-abrams-core_2.12-1.13.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/googlecode/java-diff-utils/diffutils/1.3.0/diffutils-1.3.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scalameta/scalafmt-sysops_2.12/3.7.13/scalafmt-sysops_2.12-3.7.13.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scalameta/scalafmt-dynamic_2.12/3.7.13/scalafmt-dynamic_2.12-3.7.13.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/vdurmont/semver4j/3.1.0/semver4j-3.1.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/proxy-scala-sbt-releases/com.typesafe.sbt/sbt-git/scala_2.12/sbt_1.0/1.0.0/jars/sbt-git.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scalastyle/scalastyle_2.12/1.0.0/scalastyle_2.12-1.0.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/lib/scala-library.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scala-lang/modules/scala-xml_2.12/2.3.0/scala-xml_2.12-2.3.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/eed3si9n/jarjar/jarjar/1.13.1/jarjar-1.13.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/lib/scala-reflect.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/github/bigwheel/util-backports_2.12/2.1/util-backports_2.12-2.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scalameta/scalafmt-interfaces/3.7.13/scalafmt-interfaces-3.7.13.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/io/get-coursier/interface/0.0.17/interface-0.0.17.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/typesafe/config/1.4.2/config-1.4.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/eclipse/jgit/org.eclipse.jgit/4.9.0.201710071750-r/org.eclipse.jgit-4.9.0.201710071750-r.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scalariform/scalariform_2.12/0.2.0/scalariform_2.12-0.2.0.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/ow2/asm/asm/9.6/asm-9.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/ow2/asm/asm-commons/9.6/asm-commons-9.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/commons/commons-lang3/3.8.1/commons-lang3-3.8.1.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/jcraft/jsch/0.1.54/jsch-0.1.54.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/com/googlecode/javaewah/JavaEWAH/1.1.6/JavaEWAH-1.1.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/httpcomponents/httpclient/4.3.6/httpclient-4.3.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/slf4j/slf4j-api/1.7.2/slf4j-api-1.7.2.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/scala-lang/modules/scala-parser-combinators_2.12/1.0.6/scala-parser-combinators_2.12-1.0.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/ow2/asm/asm-tree/9.6/asm-tree-9.6.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/org/apache/httpcomponents/httpcore/4.3.3/httpcore-4.3.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/commons-logging/commons-logging/1.1.3/commons-logging-1.1.3.jar:/home/<USER>/.cache/coursier/v1/https/repository.rnd.amadeus.net/sbt-sonatype-remote/commons-codec/commons-codec/1.6/commons-codec-1.6.jar:/home/<USER>/.sbt/boot/scala-2.12.18/lib/scala-xml_2.12-2.1.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/lib/scala-compiler.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/jline-terminal-3.19.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/reactive-streams-1.0.3.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/ivy-2.3.0-sbt-396a783bba347016e7fe30dacc60d355be607fe2.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/util-relation_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/error_prone_annotations-2.4.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/zinc-lm-integration_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/main-settings_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/jline-reader-3.19.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/sjson-new-murmurhash_2.12-0.9.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/scripted-plugin_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/util-interface-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/template-resolver-0.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/zinc-classpath_2.12-1.9.5.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/scala-compiler-2.12.18.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/jline-style-3.19.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/main_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/zinc-core_2.12-1.9.5.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/core-macros_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/zinc-apiinfo_2.12-1.9.5.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/util-logging_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/task-system_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/testing_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/scala-xml_2.12-2.2.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/shaded-scalajson_2.12-1.0.0-M4.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/logic_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/util-position_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/jsch-0.1.54.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/jline-builtins-3.19.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/zinc-persist-core-assembly-1.9.5.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/log4j-slf4j-impl-2.17.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/jansi-2.1.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/ssl-config-core_2.12-0.6.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/librarymanagement-ivy_2.12-1.9.3.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/shaded-apache-httpasyncclient-0.7.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/sjson-new-core_2.12-0.9.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/jline-terminal-jna-3.19.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/caffeine-2.8.5.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/zinc-compile-core_2.12-1.9.5.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/scala-library-2.12.18.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/zinc-compile_2.12-1.9.5.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/zinc-persist_2.12-1.9.5.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/checker-qual-3.4.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/scala-collection-compat_2.12-2.10.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/collections_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/scala-parser-combinators_2.12-1.1.2.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/jna-5.13.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/jline-2.14.7-sbt-a1b0ffbb8f64bb820f4f84a0c07a0c0964507493.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/sbt-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/util-control_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/lm-coursier-shaded_2.12-2.1.2.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/disruptor-3.4.2.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/protocol_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/file-tree-views-2.1.12.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/slf4j-api-1.7.36.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/librarymanagement-core_2.12-1.9.3.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/sjson-new-scalajson_2.12-0.9.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/actions_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/sbinary_2.12-0.5.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/scala-reflect-2.12.18.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/util-tracking_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/test-interface-1.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/jline-terminal-jansi-3.19.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/shaded-jawn-parser_2.12-0.9.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/command_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/zero-allocation-hashing-0.10.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/gigahorse-core_2.12-0.7.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/ipcsocket-1.6.2.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/jna-platform-5.13.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/compiler-bridge_2.12-1.9.5.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/launcher-interface-1.4.2.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/config-1.4.2.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/io_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/log4j-core-2.17.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/tasks_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/gigahorse-apache-http_2.12-0.7.0.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/compiler-interface-1.9.5.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/completion_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/test-agent-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/util-cache_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/log4j-api-2.17.1.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/zinc-classfile_2.12-1.9.5.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/run_2.12-1.9.8.jar:/home/<USER>/.sbt/boot/scala-2.12.18/org.scala-sbt/sbt/1.9.8/zinc_2.12-1.9.5.jar
